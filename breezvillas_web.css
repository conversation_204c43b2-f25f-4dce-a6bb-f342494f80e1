.navbar-brand.has-brand-image {
  display: block;
  padding: 0;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: left center;
  height: 100px !important;
  transition: height .2s ease;
}

h1.page-header {
  text-align: center;
  font-size: 60px;
  font-weight: 600;
  line-height: 1.2;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  h1.page-header {
      font-size: 50px;
  }
}

@media (max-width: 768px) {
  h1.page-header {
      font-size: 40px;
  }
}

@media (max-width: 480px) {
  h1.page-header {
      font-size: 30px;
  }
}
/* BEGIN LANDING PAGE 2024 Google Ad-1 */
.bv-lp-container {
          max-width: 1200px;
          margin: auto;
          padding: 20px;
          display: flex;
          flex-wrap: wrap;
          gap: 20px;
      }

      .bv-lp-left {
          flex: 1;
          min-width: 300px;
      }

      .bv-lp-right {
          flex: 0 0 350px;
          /* Set a fixed width for the right section on larger screens */
          min-width: 250px;
          position: sticky;
          top: 20px;
          align-self: flex-start;
          /* Ensures it stays at the top of the scroll */
      }

      .bv-lp-header {
          text-align: center;
          margin-bottom: 20px;
      }

      .bv-lp-features {
          display: flex;
          flex-wrap: wrap;
          gap: 10px;
          margin-bottom: 20px;
          justify-content: center;
      }

      .bv-lp-feature-item {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 10px;
          border: 1px solid #ddd;
          border-radius: 5px;
          background: #f9f9f9;
          min-width: 180px;
      }

      .bv-lp-feature-item span {
          font-size: 20px;
          /* Icon size */
      }

      .bv-lp-feature-item p {
          margin: 0;
      }

      .bv-lp-collage {
          display: flex;
          flex-wrap: wrap;
          gap: 10px;
          margin-bottom: 20px;
      }

      .bv-lp-collage img {
          width: 100%;
          max-width: calc(50% - 10px);
          border-radius: 5px;
      }

      .bv-lp-booking-section {
          padding: 20px;
          border: 1px solid #ddd;
          border-radius: 5px;
          background: #f9f9f9;
      }

      .bv-lp-booking-title {
          font-size: 24px;
          margin-bottom: 10px;
          text-align: center;
      }

      .bv-lp-booking-form {
          overflow: hidden;
          margin: auto;
      }

      .bv-lp-book-now-btn {
          display: inline-block;
          padding: 10px 20px;
          background: #007BFF;
          color: #fff;
          text-decoration: none;
          border-radius: 5px;
          margin-top: 20px;
          text-align: center;
      }

      .iframe-container {
          position: relative;
          width: 100%;
          padding-bottom: 56.25%;
          /* 16:9 Aspect Ratio */
          margin-top: 10px;
          border-radius: 5px;
          overflow: hidden;
      }

      .iframe-container iframe {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          border: 0;
      }

      @media (max-width: 768px) {
          .bv-lp-container {
              flex-direction: column;
          }

          .bv-lp-right {
              position: static;
              /* Disable sticky behavior on smaller screens */
              width: 100%;
          }

          .bv-lp-collage img {
              max-width: 100%;
          }

          .bv-lp-feature-item {
              min-width: 100%;
          }
      }
/* END OF LANDING PAGE 2024 1 - GOOGLE AD*/

/** WHATSAPP BUTTON **/
#whatsapp-btn {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
}

#whatsapp-link {
  display: flex;
  align-items: center;
  background-color: #25D366;
  border-radius: 50px;
  padding: 10px 20px;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2);
  text-decoration: none;
  color: white;
  font-family: Arial, sans-serif;
  font-size: 16px;
  font-weight: bold;
}

#whatsapp-icon {
  width: 30px;
  height: 30px;
  margin-right: 10px;
}

#whatsapp-link:hover {
  background-color: #1EBE5B;
  transition: 0.3s ease;
}

.breezvillas-container {
  max-width: 99%;
  /**width: 600px;**/
  background: white;
  padding: 20px;
  border-radius: 15px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  border: 1px solid #ccc;
  margin-bottom: 20px;
}
.breezvillas-container h2 {
  color: #333;
  font-size: 1.8em;
}
.breezvillas-container p {
  color: #666;
  font-size: 1.2em;
  line-height: 1.5;
}
.breezvillas-highlight {
  font-weight: bold;
  color: #007bff;
}
.breezvillas-addons {
  display: flex;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
}
.breezvillas-addon {
  background: white;
  padding: 15px;
  border-radius: 10px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  border: 1px solid #ccc;
  width: 280px;
  text-align: center;
}
.breezvillas-addon h3 {
  color: #333;
  font-size: 1.5em;
}
.breezvillas-addon p {
  font-size: 1.2em;
  color: #666;
}
@media (max-width: 600px) {
  .breezvillas-container {
      padding: 15px;
      width: 95%;
  }
  .breezvillas-container h2 {
      font-size: 1.5em;
  }
  .breezvillas-container p {
      font-size: 1em;
  }
  .breezvillas-addon {
      width: 90%;
  }
}
.cookie-consent {
  position: fixed;
  bottom: 20px;
  left: 20px;
  right: 20px;
  background: #333;
  color: #fff;
  padding: 15px;
  text-align: center;
  display: none;
  border-radius: 5px;
}
.cookie-consent button {
  background: #ff9800;
  border: none;
  padding: 10px 15px;
  cursor: pointer;
  margin-left: 10px;
  border-radius: 3px;
}
/** END WHATSAPP BUTTON **/

/*BeginTemplate*/ .section-right .header-links { font-size: 1.6em; } .section-right .header-links .btn { font-size: 0.6em; }/*EndTemplate*/

/* BEGIN DEAL PAGE STYLES 2024 */

/* Base Styles for Deal Page */

/* Container Layout */
.bv-deal-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    font-family: Arial, Helvetica, sans-serif;
    line-height: 1.6;
    color: #333;
    font-size: 16px;
}

/* Header Styles */
.bv-deal-header {
    margin-bottom: 30px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.bv-deal-header-columns {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    width: 100% !important;
    margin-bottom: 30px;
    flex-wrap: nowrap !important;
}

.bv-deal-header-left {
    flex: 1 !important;
    text-align: left !important;
    display: flex !important;
    align-items: center !important;
}

.bv-deal-header-right {
    flex: 0 0 auto !important;
    text-align: right !important;
    display: flex !important;
    align-items: center !important;
}

.bv-deal-logo-container {
    text-align: left;
    margin-bottom: 0;
    width: auto;
}

.bv-deal-logo-link {
    display: inline-block;
    width: auto;
    text-align: left;
}

.bv-deal-logo {
    width: 400px;
    max-width: 100%;
    height: auto;
}

.bv-deal-badge-img {
    height: 150px !important;
    width: auto !important;
    max-width: 150px !important;
    border: none !important;
}

.bv-deal-cta-header {
    text-align: center;
    margin: 20px 0;
}

/* Main Title Section */
.bv-deal-main-title-section {
    text-align: center;
    margin-bottom: 30px;
    margin-top: 20px;
    max-width: 1000px;
    margin-left: auto;
    margin-right: auto;
    padding: 0 20px;
}

.bv-deal-main-title {
    font-size: 36px;
    font-weight: 700;
    color: #333;
    margin-bottom: 20px;
    font-family: Arial, Helvetica, sans-serif;
    line-height: 1.3;
}

.bv-deal-main-description {
    font-size: 18px;
    line-height: 1.6;
    color: #555;
    font-family: Arial, Helvetica, sans-serif;
    margin-bottom: 10px;
}

.bv-deal-book-btn {
    display: inline-block;
    padding: 12px 25px;
    background-color: #e63946; /* Red color */
    color: white;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
    font-family: Arial, Helvetica, sans-serif;
    font-size: 16px;
    transition: background-color 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.bv-deal-book-btn:hover {
    background-color: #c1121f; /* Darker red on hover */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Section Titles */
.bv-deal-section-title {
    text-align: center;
    font-size: 28px;
    margin-bottom: 30px;
    position: relative;
    padding-bottom: 10px;
    font-family: Arial, Helvetica, sans-serif;
    font-weight: 600;
    color: #333;
}

.bv-deal-section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background-color: #007BFF;
}

/* Feature Cards */
.bv-deal-features-section {
    margin-bottom: 40px;
}

.bv-deal-features-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 25px;
    max-width: 1100px;
    margin: 0 auto;
}

.bv-deal-feature-card {
    padding: 25px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background-color: #f9f9f9;
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.bv-deal-feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.bv-deal-feature-icon {
    width: 60px;
    height: 60px;
    margin-bottom: 15px;
}

.bv-deal-feature-title {
    font-size: 20px;
    margin: 10px 0;
    font-family: Arial, Helvetica, sans-serif;
    font-weight: 600;
    color: #333;
}

.bv-deal-feature-desc {
    color: #666;
    font-size: 16px;
    font-family: Arial, Helvetica, sans-serif;
    line-height: 1.5;
}

/* Booking Channels Section */
.bv-deal-booking-channels {
    margin-bottom: 40px;
    text-align: center;
}

.bv-deal-channels-desc {
    max-width: 700px;
    margin: 0 auto 30px;
    font-size: 18px;
    font-family: Arial, Helvetica, sans-serif;
    line-height: 1.5;
    color: #333;
}

.bv-deal-channels-logos {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 40px;
    margin: 40px 0;
    text-align: center;
}

.bv-deal-channel-logo {
    height: 50px;
    width: auto;
    vertical-align: middle;
    object-fit: contain;
}

/* CTA Box */
.bv-deal-cta-box {
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 15px 20px;
    margin: 30px auto;
    max-width: 800px;
    box-sizing: border-box;
}

.bv-deal-cta-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    padding: 0;
    margin: 0;
}

.bv-deal-cta-text {
    flex: 1;
    text-align: left;
    padding: 0 20px 0 0;
    min-width: 300px;
}

.bv-deal-cta-button {
    flex: 0 0 auto;
    padding: 0;
    margin: 0;
}

.bv-deal-cta-title {
    font-size: 24px;
    margin: 0 0 5px 0;
    color: #e63946;
    font-family: Arial, Helvetica, sans-serif;
    font-weight: 600;
}

.bv-deal-cta-subtitle {
    font-size: 16px;
    margin: 0;
    color: #666;
    font-family: Arial, Helvetica, sans-serif;
    line-height: 1.5;
}

/* Newsletter Section */
.bv-deal-newsletter {
    margin-bottom: 40px;
    text-align: center;
}

/* MailerLite Form Styling */
.ml-embedded {
    max-width: 600px;
    margin: 0 auto 20px;
}

.bv-deal-form-disclaimer {
    font-size: 14px;
    color: #666;
    margin: 0 auto;
    max-width: 600px;
    font-family: Arial, Helvetica, sans-serif;
    line-height: 1.5;
    text-align: center;
}

.bv-deal-thank-you {
    padding: 20px;
    background-color: #e8f5e9;
    border-radius: 5px;
    margin-top: 20px;
}

/* Resort Section */
.bv-deal-resort {
    margin-bottom: 40px;
}

.bv-deal-resort-content {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    align-items: flex-start;
}

.bv-deal-resort-img-link {
    flex: 1;
    min-width: 600px; /* Increased minimum width */
    margin-bottom: 30px;
}

.bv-deal-resort-img {
    width: 100%;
    border-radius: 5px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.bv-deal-amenities-list {
    flex: 1;
    min-width: 300px;
    list-style: none;
    padding: 0;
    margin-top: 20px;
}

.bv-deal-amenity-item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    font-family: Arial, Helvetica, sans-serif;
    font-size: 18px;
    color: #333;
}

.bv-deal-check-icon {
    width: 24px;
    height: 24px;
    margin-right: 15px;
}

/* Reviews Section */
.bv-deal-reviews {
    margin-bottom: 40px;
}

.bv-deal-reviews-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
    max-width: 1000px;
    margin: 0 auto;
}

.bv-deal-review-card {
    padding: 25px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background-color: #f9f9f9;
}

.bv-deal-review-text {
    font-style: italic;
    margin-bottom: 15px;
    font-family: Arial, Helvetica, sans-serif;
    line-height: 1.5;
    color: #333;
}

.bv-deal-reviewer {
    font-weight: bold;
    text-align: right;
    margin: 0;
    font-family: Arial, Helvetica, sans-serif;
    color: #333;
}

/* Responsive Adjustments for Deal Page */
@media (max-width: 1024px) {
    .bv-deal-features-grid {
        grid-template-columns: repeat(2, 1fr);
        max-width: 800px;
    }
}

@media (max-width: 768px) {
    .bv-deal-features-grid {
        grid-template-columns: 1fr;
        max-width: 500px;
    }

    .bv-deal-reviews-grid {
        grid-template-columns: 1fr;
        max-width: 500px;
    }

    .bv-deal-header-columns {
        flex-direction: column;
        align-items: center;
        gap: 20px;
    }

    .bv-deal-header-left {
        text-align: center;
    }

    .bv-deal-header-right {
        text-align: center;
    }

    .bv-deal-logo-container {
        text-align: center;
    }

    .bv-deal-logo-link {
        text-align: center;
    }

    .bv-deal-logo {
        width: 350px;
    }

    .bv-deal-badge-img {
        height: 120px !important;
        max-width: 120px !important;
    }

    .bv-deal-main-title {
        font-size: 30px;
    }

    .bv-deal-main-description {
        font-size: 16px;
    }

    .bv-deal-section-title {
        font-size: 24px;
    }

    .bv-deal-channels-logos {
        gap: 20px;
    }

    .bv-deal-channel-logo {
        width: 150px;
    }

    .bv-deal-cta-box {
        padding: 15px;
        margin: 20px auto;
    }

    .bv-deal-cta-content {
        flex-direction: column;
        align-items: center;
    }

    .bv-deal-cta-text {
        text-align: center;
        padding: 0 0 15px 0;
        margin: 0;
    }

    .bv-deal-resort-content {
        flex-direction: column;
    }

    .bv-deal-resort-img-link {
        min-width: 100%;
    }

    .bv-deal-amenity-item {
        font-size: 16px;
    }

    .bv-deal-book-btn {
        padding: 10px 20px;
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .bv-deal-container {
        padding: 15px;
    }

    .bv-deal-logo {
        width: 280px;
    }

    .bv-deal-badge-img {
        height: 100px !important;
        max-width: 100px !important;
    }

    .bv-deal-main-title {
        font-size: 24px;
    }

    .bv-deal-main-description {
        font-size: 15px;
        line-height: 1.5;
    }

    .bv-deal-section-title {
        font-size: 20px;
    }

    .bv-deal-feature-title {
        font-size: 18px;
    }

    .bv-deal-cta-title {
        font-size: 20px;
    }

    .bv-deal-channel-logo {
        width: 120px;
    }

    .bv-deal-channels-logos {
        gap: 15px;
    }

    .bv-deal-feature-card {
        padding: 15px;
    }

    .bv-deal-review-card {
        padding: 15px;
    }
}
/* END DEAL PAGE STYLES 2024 */

/* BEGIN BLOG STYLES 2024 */
.bv-blog-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 30px;
    font-family: Arial, Helvetica, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
}

.bv-blog-intro {
    font-size: 13pt;
    line-height: 1.6;
    margin-bottom: 30px;
    color: #333;
}

.bv-blog-title {
    font-size: 32px;
    font-weight: 700;
    color: #333;
    margin-bottom: 30px;
    line-height: 1.3;
    text-align: center;
    font-family: Arial, Helvetica, sans-serif;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.bv-blog-subtitle, h3.bv-blog-subtitle {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin: 40px 0 20px;
    position: relative;
    padding-bottom: 10px;
    font-family: Arial, Helvetica, sans-serif;
}

.bv-blog-subtitle::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 80px;
    height: 3px;
    background-color: #007BFF;
}

.bv-blog-content {
    font-size: 13pt;
    line-height: 1.6;
    margin-bottom: 20px;
    font-family: Arial, Helvetica, sans-serif;
    color: #333;
}

.bv-blog-list {
    margin: 20px 0;
    padding-left: 20px;
}

.bv-blog-list li {
    margin-bottom: 10px;
    font-size: 13pt;
    font-family: Arial, Helvetica, sans-serif;
    color: #333;
}

.bv-blog-image {
    display: block;
    width: 100%;
    height: 400px; /* Fixed height for all images */
    object-fit: cover; /* Maintain aspect ratio and cover container */
    border-radius: 8px;
    margin: 30px auto;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.bv-blog-highlight {
    background-color: #f8f9fa;
    border-left: 4px solid #007BFF;
    padding: 20px;
    margin: 30px 0;
    font-size: 13pt;
    line-height: 1.6;
    font-family: Arial, Helvetica, sans-serif;
    color: #333;
}

.bv-blog-cta {
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 25px;
    margin: 40px 0;
    text-align: center;
}

.bv-blog-cta-title {
    font-size: 22px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
}

.bv-blog-cta-button {
    display: inline-block;
    padding: 12px 25px;
    background-color: #e63946; /* Red color */
    color: white;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
    font-size: 16px;
    transition: background-color 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 15px;
}

.bv-blog-cta-button:hover {
    background-color: #c1121f; /* Darker red on hover */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Table of Contents */
.bv-blog-toc {
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin: 30px 0;
}

.bv-blog-toc-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
    font-family: Arial, Helvetica, sans-serif;
}

.bv-blog-toc-list {
    list-style-type: none;
    padding-left: 0;
}

.bv-blog-toc-list li {
    margin-bottom: 10px;
    font-size: 13pt;
    font-family: Arial, Helvetica, sans-serif;
}

.bv-blog-toc-list a {
    color: #007BFF;
    text-decoration: none;
    transition: color 0.3s ease;
}

.bv-blog-toc-list a:hover {
    color: #0056b3;
    text-decoration: underline;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .bv-blog-title {
        font-size: 28px;
    }

    .bv-blog-subtitle {
        font-size: 22px;
    }

    .bv-blog-image {
        height: 300px;
    }
}

@media (max-width: 480px) {
    .bv-blog-title {
        font-size: 24px;
    }

    .bv-blog-subtitle {
        font-size: 20px;
    }

    .bv-blog-image {
        height: 250px;
    }

    .bv-blog-container {
        padding: 15px;
    }
}
/* END BLOG STYLES 2024 */