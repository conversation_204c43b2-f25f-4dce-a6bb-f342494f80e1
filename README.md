# Breez Villas Deal Page

This is a replica of the Breez Villas deal page (https://campaign.breezvillas.com/deal) created with HTML and CSS.

## Files

- `deal.html` - The main HTML file that contains the structure of the page
- `breezvillas_web.css` - Combined CSS file containing all styles for the website, including the deal page
- `deal.js` - JavaScript file for interactive elements

## Features

1. **Responsive Design**: The page is fully responsive and works on all device sizes
2. **Reusable Components**: CSS is organized with reusable classes and variables
3. **Email Marketing Integration**: A placeholder is provided for your email marketing plugin
4. **Interactive Elements**: Smooth scrolling and hover effects

## How to Use

1. Open `deal.html` in a web browser to view the page
2. To add your email marketing plugin, replace the placeholder in the Newsletter section:

```html
<!-- Placeholder for email marketing plugin -->
<div class="bv-deal-newsletter-placeholder">
    <!-- Your email marketing plugin will be inserted here -->
    <p class="bv-deal-form-disclaimer">You can unsubscribe anytime. For more details, review our Privacy Policy.</p>
</div>
```

## CSS Structure

The CSS for the deal page has been integrated into the main `breezvillas_web.css` file. All deal page styles are contained within a clearly marked section:

```css
/* BEGIN DEAL PAGE STYLES 2024 */
/* ... deal page styles ... */
/* END DEAL PAGE STYLES 2024 */
```

All styles use direct color values and measurements for maximum compatibility. The styles are namespaced with the `bv-deal-` prefix to avoid conflicts with existing styles.

## Sections

The page is divided into several sections:

1. Header with logo and book now button
2. Property Features with 5 feature cards
3. Booking Channels with logos and CTA
4. Newsletter signup (placeholder for your plugin)
5. Resort information with amenities list
6. Guest Reviews
7. Thank You section

## Customization

To customize the page:

1. Update image URLs in the HTML file
2. Modify text content as needed
3. Adjust CSS variables in `deal_styles.css` to change colors, spacing, etc.
4. Add your email marketing plugin code to the newsletter section
