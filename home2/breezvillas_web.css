/* Cursor.com-inspired <PERSON><PERSON> CSS - Light Mode */
:root {
  --primary-bg: #ffffff;
  --secondary-bg: #fafafa;
  --accent-bg: #f5f5f7;
  --text-primary: #1d1d1f;
  --text-secondary: #86868b;
  --accent-color: #004aad;
  --accent-hover: #003d8a;
  --accent-secondary: #4e30b2;
  --accent-tertiary: #8203a6;
  --border-color: #e5e5e7;
  --card-bg: #ffffff;
  --gradient-primary: linear-gradient(135deg, #004aad 0%, #4e30b2 100%);
  --gradient-secondary: linear-gradient(135deg, #4e30b2 0%, #8203a6 100%);
  --gradient-tertiary: linear-gradient(135deg, #fafafa 0%, #f5f5f7 100%);
  --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.04);
  --shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.08);
  --shadow-heavy: 0 12px 40px rgba(0, 0, 0, 0.12);
  --shadow-premium: 0 20px 60px rgba(0, 0, 0, 0.15);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Inter', 'Segoe UI', Roboto, sans-serif;
  background: var(--primary-bg);
  color: var(--text-primary);
  line-height: 1.6;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Container */
.bv-deal-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  position: relative;
}

/* Header */
.bv-deal-header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 32px 0;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 80px;
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95);
  transition: all 0.3s ease;
}

.bv-deal-header-single {
  display: flex;
  align-items: center;
}

.bv-deal-badge-img {
  height: 300px;
  width: auto;
  border-radius: 12px;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.1));
}

.bv-deal-badge-img:hover {
  transform: scale(1.05) translateY(-2px);
  filter: drop-shadow(0 8px 24px rgba(0, 0, 0, 0.15));
}

.bv-deal-cta-header {
  display: flex;
  align-items: center;
}

/* Main Title Section */
.bv-deal-main-title-section {
  text-align: center;
  padding: 140px 0 100px;
  background: linear-gradient(180deg, #fafafa 0%, #f5f5f7 100%);
  border-radius: 32px;
  margin-bottom: 100px;
  position: relative;
  overflow: hidden;
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-light);
}

.bv-deal-main-title-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%, rgba(0, 74, 173, 0.03) 0%, transparent 50%),
              radial-gradient(circle at 70% 80%, rgba(78, 48, 178, 0.03) 0%, transparent 50%);
  pointer-events: none;
}

.bv-deal-main-title {
  font-size: 4.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 32px;
  position: relative;
  z-index: 1;
  letter-spacing: -0.025em;
  line-height: 1.1;
}

.bv-deal-main-description {
  font-size: 1.5rem;
  color: var(--text-secondary);
  max-width: 800px;
  margin: 0 auto 48px;
  position: relative;
  z-index: 1;
  line-height: 1.6;
  font-weight: 400;
}

.bv-deal-main-cta {
  position: relative;
  z-index: 1;
  margin-top: 48px;
}

/* Buttons */
.bv-deal-book-btn {
  display: inline-block;
  padding: 20px 40px;
  background: var(--gradient-primary);
  color: #ffffff;
  text-decoration: none;
  border-radius: 16px;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border: none;
  cursor: pointer;
  box-shadow: var(--shadow-medium);
  position: relative;
  overflow: hidden;
  letter-spacing: -0.01em;
}

.bv-deal-book-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.bv-deal-book-btn:hover::before {
  left: 100%;
}

.bv-deal-book-btn:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-premium);
  background: var(--gradient-secondary);
}

/* Section Titles */
.bv-deal-section-title {
  font-size: 3.75rem;
  font-weight: 700;
  text-align: center;
  margin-top: 0;
  margin-bottom: 28px;
  color: var(--text-primary);
  position: relative;
  letter-spacing: -0.025em;
  line-height: 1.1;
}

/* Features Section */
.bv-deal-features-section {
  margin-bottom: 48px;
  padding: 24px 0 0 0;
}

.bv-deal-features-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-auto-rows: 1fr;
  gap: 28px;
  margin-top: 32px;
}

.bv-deal-feature-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 24px;
  padding: 28px 20px;
  text-align: center;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-light);
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: calc(100% - 24px);
}

.bv-deal-feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gradient-primary);
  transform: scaleX(0);
  transition: transform 0.4s ease;
}

.bv-deal-feature-card:hover::before {
  transform: scaleX(1);
}

.bv-deal-feature-card:hover {
  transform: translateY(-12px);
  box-shadow: var(--shadow-premium);
  border-color: var(--accent-color);
}

.bv-deal-feature-icon {
  width: 56px;
  height: 56px;
  margin-bottom: 18px;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.1));
}

.bv-deal-feature-card:hover .bv-deal-feature-icon {
  transform: scale(1.1) translateY(-4px);
  filter: drop-shadow(0 8px 24px rgba(0, 0, 0, 0.15));
}

.bv-deal-feature-title {
  font-size: 1.75rem;
  font-weight: 600;
  margin-bottom: 24px;
  color: var(--text-primary);
  letter-spacing: -0.01em;
}

.bv-deal-feature-desc {
  color: var(--text-secondary);
  line-height: 1.7;
  font-size: 1.125rem;
  font-weight: 400;
}

/* Booking Channels */
.bv-deal-booking-channels {
  margin-bottom: 36px;
  padding: 24px 0 0 0;
  background: linear-gradient(180deg, #fafafa 0%, #f5f5f7 100%);
  border-radius: 32px;
  border: 1px solid var(--border-color);
  position: relative;
  overflow: hidden;
}

.bv-deal-booking-channels::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 30%, rgba(0, 74, 173, 0.02) 0%, transparent 50%),
              radial-gradient(circle at 80% 70%, rgba(78, 48, 178, 0.02) 0%, transparent 50%);
  pointer-events: none;
}

.bv-deal-channels-desc {
  text-align: center;
  font-size: 1.375rem;
  color: var(--text-secondary);
  margin-bottom: 80px;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
  font-weight: 400;
  position: relative;
  z-index: 1;
}

.bv-deal-channels-logos {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 48px;
  margin: 80px 0;
  flex-wrap: wrap;
  position: relative;
  z-index: 1;
  padding: 60px 40px;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 24px;
  box-shadow: var(--shadow-light);
}

.bv-deal-channel-logo {
  height: 80px;
  width: auto;
  filter: brightness(0.7);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  opacity: 0.8;
  padding: 20px;
}

.bv-deal-channel-link {
  display: block;
  text-decoration: none;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.bv-deal-channel-link:hover .bv-deal-channel-logo {
  filter: brightness(1);
  transform: scale(1.15) translateY(-2px);
  opacity: 1;
  box-shadow: var(--shadow-medium);
}

/* CTA Box */
.bv-deal-cta-box {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 24px;
  padding: 60px;
  margin: 60px 0;
  text-align: center;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-medium);
  backdrop-filter: blur(20px);
}

.bv-deal-cta-box::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 74, 173, 0.02) 0%, rgba(78, 48, 178, 0.02) 100%);
  pointer-events: none;
}

.bv-deal-cta-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
}

.bv-deal-cta-text {
  text-align: left;
}

.bv-deal-cta-title {
  font-size: 2.75rem;
  font-weight: 700;
  margin-bottom: 16px;
  color: var(--text-primary);
  letter-spacing: -0.025em;
  line-height: 1.1;
}

.bv-deal-cta-subtitle {
  color: var(--text-secondary);
  font-size: 1.25rem;
  font-weight: 400;
}

.bv-deal-cta-button {
  flex-shrink: 0;
}

/* Resort Section */
.bv-deal-resort {
  margin-bottom: 12px;
  padding: 24px 0 0 0;
}

.bv-deal-resort-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: stretch;
}

.bv-deal-resort-img-link {
  display: block;
  border-radius: 24px;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: var(--shadow-medium);
  height: 100%;
}

.bv-deal-resort-img-link:hover {
  transform: scale(1.02);
  box-shadow: var(--shadow-premium);
}

.bv-deal-resort-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 24px;
  transition: transform 0.4s ease;
}

.bv-deal-amenities-list {
  list-style: none;
  display: grid;
  gap: 24px;
  height: 100%;
  align-content: center;
}

.bv-deal-amenity-item {
  display: flex;
  align-items: center;
  gap: 24px;
  padding: 24px;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 20px;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: var(--shadow-light);
}

.bv-deal-amenity-item:hover {
  border-color: var(--accent-color);
  transform: translateX(8px);
  box-shadow: var(--shadow-medium);
}

.bv-deal-check-icon {
  width: 28px;
  height: 28px;
  filter: brightness(0) saturate(100%) invert(0%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(0%) contrast(100%);
}

/* Reviews Section */
.bv-deal-reviews {
  margin-bottom: 48px;
  padding: 0 0 0 0;
}

.bv-deal-reviews-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-auto-rows: 1fr;
  gap: 28px;
  margin-top: 32px;
  align-items: start;
}

/* Review Cards with Colorful Backgrounds */
.bv-deal-review-card-new {
  background: linear-gradient(135deg, #f5f5f5 0%, #ffffff 100%);
  border: 1px solid var(--border-color);
  border-radius: 20px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.07);
  padding: 32px 28px;
  height: 440px;
  margin: 0;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease;
}

/* Hover effects for review cards */
.bv-deal-review-card-new:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(0,0,0,0.12);
  background: linear-gradient(135deg, #eeeeee 0%, #f8f8f8 100%);
}

/* Adjust text colors for better contrast on blue background */
.bv-deal-review-card-new .bv-deal-review-title {
  color: var(--text-primary);
}

/* Adjust rating colors to match blue theme */
.bv-deal-review-card-new .bv-deal-rating-score,
.bv-deal-review-card-new .bv-deal-star {
  color: var(--accent-color);
}

/* Adjust reviewer name colors */
.bv-deal-review-card-new .bv-deal-reviewer-name {
  color: var(--accent-color);
}

/* Adjust divider colors to match blue theme */
.bv-deal-review-card-new .bv-deal-review-header-divider,
.bv-deal-review-card-new .bv-deal-review-footer-divider {
  background: var(--border-color);
}

.bv-deal-review-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 18px;
  width: 100%;
}

.bv-deal-rating-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.bv-deal-rating-score {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--accent-color);
}

.bv-deal-rating-stars {
  display: flex;
  gap: 2px;
}

.bv-deal-star {
  color: var(--accent-color);
  font-size: 1.1rem;
}

.bv-deal-review-date {
  color: var(--text-secondary);
  font-size: 1rem;
  font-weight: 500;
  margin-left: 16px;
  white-space: nowrap;
}

.bv-deal-review-header-divider {
  width: 100%;
  height: 1px;
  background: var(--border-color);
  margin: 12px 0 18px 0;
}

.bv-deal-review-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 10px;
}

.bv-deal-review-text-new {
  color: var(--text-secondary);
  font-size: 1.05rem;
  margin-bottom: 18px;
  line-height: 1.6;
}

.bv-deal-review-footer {
  margin-top: auto;
  width: 100%;
}

.bv-deal-review-footer-divider {
  width: 100%;
  height: 1px;
  background: var(--border-color);
  margin-bottom: 10px;
}

.bv-deal-reviewer-name {
  font-weight: 700;
  color: var(--accent-color);
  font-size: 1.1rem;
  letter-spacing: 0.01em;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .bv-deal-features-grid,
  .bv-deal-reviews-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
  
  .bv-deal-resort-content {
    grid-template-columns: 1fr;
  }
  
  .bv-deal-review-card-new {
    padding: 20px 10px;
    height: 340px;
  }
  
  .bv-deal-review-title { font-size: 1.1rem; }
  .bv-deal-review-text-new { font-size: 1rem; }
  
  .bv-deal-resort-img-link {
    height: 300px;
  }
}

@media (max-width: 768px) {
  .bv-deal-main-title {
    font-size: 3.25rem;
  }
  
  .bv-deal-main-description {
    font-size: 1.25rem;
  }
  
  .bv-deal-section-title {
    font-size: 2.25rem;
    margin-bottom: 16px;
  }
  
  .bv-deal-features-grid,
  .bv-deal-reviews-grid {
    grid-template-columns: 1fr;
    gap: 14px;
  }
  
  .bv-deal-channels-logos {
    gap: 32px;
  }
  
  .bv-deal-channel-logo {
    height: 60px;
  }
  
  .bv-deal-cta-content {
    flex-direction: column;
    gap: 32px;
  }
  
  .bv-deal-cta-text {
    text-align: center;
  }
  
  .bv-deal-header {
    flex-direction: column;
    gap: 24px;
  }
  
  .bv-deal-badge-img {
    height: 200px;
  }
  
  .bv-deal-features-section,
  .bv-deal-reviews,
  .bv-deal-resort,
  .bv-deal-booking-channels {
    margin-bottom: 24px;
    padding: 12px 0 0 0;
  }
  
  .bv-deal-standalone-cta {
    margin-bottom: 20px;
  }
  
  .bv-deal-review-card-new {
    padding: 12px 4px;
    height: 240px;
  }
  
  .bv-deal-review-title { font-size: 1rem; }
  .bv-deal-review-text-new { font-size: 0.98rem; }
  
  .bv-deal-feature-card {
    padding: 18px 10px;
    height: auto;
  }
  
  .bv-deal-feature-icon {
    width: 40px;
    height: 40px;
    margin-bottom: 12px;
  }
}

@media (max-width: 480px) {
  .bv-deal-container {
    padding: 0 16px;
  }
  
  .bv-deal-main-title {
    font-size: 2.5rem;
  }
  
  .bv-deal-main-description {
    font-size: 1.125rem;
  }
  
  .bv-deal-section-title {
    font-size: 2.25rem;
  }
  
  .bv-deal-feature-title {
    font-size: 1.5rem;
  }
  
  .bv-deal-cta-title {
    font-size: 2rem;
  }
  
  .bv-deal-channel-logo {
    height: 50px;
  }
  
  .bv-deal-feature-card {
    padding: 32px 24px;
  }
  
  .bv-deal-review-card-new {
    padding: 32px 24px;
  }
  
  .bv-deal-badge-img {
    height: 150px;
  }
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.bv-deal-feature-card,
.bv-deal-review-card-new,
.bv-deal-cta-box {
  animation: fadeInUp 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--secondary-bg);
}

::-webkit-scrollbar-thumb {
  background: var(--accent-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent-hover);
}

/* Standalone CTA Section */
.bv-deal-standalone-cta {
  margin-bottom: 12px;
  padding: 0;
}

.bv-fine-print {
  font-size: 0.95rem;
  color: var(--text-secondary);
  text-align: center;
  margin: 12px 0 24px 0;
  font-style: italic;
  letter-spacing: 0.01em;
}

/* --- Elegant Reviews Page Design --- */
.container {
  max-width: 1200px;
  margin: 60px auto 40px auto;
  padding: 48px 32px 40px 32px;
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 36px;
  box-shadow: 
    0 20px 60px rgba(0, 74, 173, 0.08),
    0 8px 24px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  position: relative;
  border: 1px solid rgba(229, 229, 231, 0.8);
  backdrop-filter: blur(20px);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 20%, rgba(0, 74, 173, 0.02) 0%, transparent 50%),
              radial-gradient(circle at 80% 80%, rgba(78, 48, 178, 0.02) 0%, transparent 50%);
  border-radius: 36px;
  pointer-events: none;
  z-index: 0;
}

.body-content {
  padding: 0 0 32px 0;
  position: relative;
  z-index: 1;
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin-left: -16px;
  margin-right: -16px;
}

.col-md-12 {
  flex: 0 0 100%;
  max-width: 100%;
  padding-left: 16px;
  padding-right: 16px;
}

.page-header {
  font-size: 3.2rem;
  font-weight: 800;
  background: linear-gradient(135deg, var(--accent-color, #004aad) 0%, var(--accent-secondary, #4e30b2) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 48px;
  letter-spacing: -0.03em;
  text-align: center;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
  position: relative;
  padding-bottom: 24px;
}

.page-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 120px;
  height: 3px;
  background: linear-gradient(90deg, var(--accent-color, #004aad) 0%, var(--accent-secondary, #4e30b2) 100%);
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(0, 74, 173, 0.3);
}

.ownerrez-widget {
  margin: 0 auto 40px auto;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 28px;
  box-shadow: 
    0 12px 40px rgba(0, 74, 173, 0.1),
    0 4px 16px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  padding: 40px 0 0 0;
  max-width: 1000px;
  border: 1px solid rgba(229, 229, 231, 0.8);
  position: relative;
  overflow: hidden;
}

.ownerrez-widget::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--accent-color, #004aad) 0%, var(--accent-secondary, #4e30b2) 100%);
  border-radius: 28px 28px 0 0;
}

.ownerrez-widget-iframe {
  border-radius: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  background: #ffffff;
  border: 1px solid rgba(229, 229, 231, 0.6);
  margin: 0 16px;
}

footer {
  background: linear-gradient(135deg, #fafbfc 0%, #f5f5f7 100%);
  border-radius: 0 0 36px 36px;
  box-shadow: 
    0 -4px 20px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  margin-top: 40px;
  padding: 40px 0 24px 0;
  font-size: 1.1rem;
  color: #6b7280;
  text-align: center;
  position: relative;
  border-top: 1px solid rgba(229, 229, 231, 0.6);
}

footer a {
  color: var(--accent-color, #004aad);
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  font-weight: 500;
  position: relative;
}

footer a:hover {
  color: var(--accent-secondary, #4e30b2);
  text-decoration: none;
  transform: translateY(-1px);
}

footer a::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 1px;
  background: linear-gradient(90deg, var(--accent-color, #004aad) 0%, var(--accent-secondary, #4e30b2) 100%);
  transition: width 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

footer a:hover::after {
  width: 100%;
}

.cookie-consent {
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  color: #ffffff;
  border-radius: 16px;
  padding: 20px 32px;
  font-size: 1.05rem;
  margin: 0 auto 24px auto;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.15),
    0 4px 16px rgba(0, 0, 0, 0.1);
  max-width: 700px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
}

.cookie-consent button {
  background: linear-gradient(135deg, var(--accent-color, #004aad) 0%, var(--accent-secondary, #4e30b2) 100%);
  color: #ffffff;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  margin-left: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 4px 12px rgba(0, 74, 173, 0.3);
  position: relative;
  overflow: hidden;
}

.cookie-consent button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.cookie-consent button:hover::before {
  left: 100%;
}

.cookie-consent button.decline {
  background: linear-gradient(135deg, #6b7280 0%, #9ca3af 100%);
  box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);
}

.cookie-consent button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 74, 173, 0.4);
}

.cookie-consent button.decline:hover {
  box-shadow: 0 8px 24px rgba(107, 114, 128, 0.4);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .container {
    margin: 40px auto 32px auto;
    padding: 32px 24px 32px 24px;
  }
  
  .page-header {
    font-size: 2.8rem;
    margin-bottom: 40px;
  }
  
  .ownerrez-widget {
    padding: 32px 0 0 0;
  }
}

@media (max-width: 768px) {
  .container {
    margin: 20px auto 20px auto;
    padding: 24px 16px 24px 16px;
    border-radius: 24px;
  }
  
  .page-header {
    font-size: 2.4rem;
    margin-bottom: 32px;
    padding-bottom: 20px;
  }
  
  .page-header::after {
    width: 80px;
  }
  
  .ownerrez-widget {
    padding: 24px 0 0 0;
    border-radius: 20px;
  }
  
  .ownerrez-widget-iframe {
    border-radius: 16px;
    margin: 0 12px;
  }
  
  footer {
    padding: 32px 0 20px 0;
    font-size: 1rem;
    border-radius: 0 0 24px 24px;
  }
  
  .cookie-consent {
    padding: 16px 24px;
    font-size: 1rem;
    margin: 0 auto 20px auto;
  }
}

@media (max-width: 480px) {
  .container {
    margin: 16px auto 16px auto;
    padding: 20px 12px 20px 12px;
    border-radius: 20px;
  }
  
  .page-header {
    font-size: 2rem;
    margin-bottom: 24px;
    padding-bottom: 16px;
  }
  
  .page-header::after {
    width: 60px;
  }
  
  .ownerrez-widget {
    padding: 20px 0 0 0;
    border-radius: 16px;
  }
  
  .ownerrez-widget-iframe {
    border-radius: 12px;
    margin: 0 8px;
  }
  
  footer {
    padding: 24px 0 16px 0;
    font-size: 0.95rem;
    border-radius: 0 0 20px 20px;
  }
  
  .cookie-consent {
    padding: 14px 20px;
    font-size: 0.95rem;
    margin: 0 auto 16px auto;
  }
  
  .cookie-consent button {
    padding: 10px 20px;
    margin-left: 8px;
  }
} 